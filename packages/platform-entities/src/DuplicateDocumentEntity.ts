import { Duplicate, DuplicateDocument } from '@bybeam/platform-types';
import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import DocumentEntity from './DocumentEntity.js';
import DuplicateEntity from './DuplicateEntity.js';

@Entity('duplicate_documents')
export class DuplicateDocumentEntity implements DuplicateDocument {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column('uuid', { name: 'duplicate_id' })
  public duplicateId: string;

  @ManyToOne(() => DuplicateEntity)
  @JoinColumn({ name: 'duplicate_id' })
  public duplicate: Duplicate;

  @Column('uuid', { name: 'document_id' })
  public documentId: string;

  @ManyToOne(() => DocumentEntity, { eager: true })
  @JoinColumn({ name: 'document_id' })
  public document: Document;
}

export default DuplicateDocumentEntity;
