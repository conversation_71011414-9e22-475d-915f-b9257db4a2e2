import { credentials, loadPackageDefinition } from '@grpc/grpc-js';
import { loadSync } from '@grpc/proto-loader';
import { ProtoGrpcType } from './generated/ers';

export * from './generated/entity_resolution/QueryRequest';
export * from './generated/entity_resolution/QueryResponse';
export * from './generated/entity_resolution/Entity';
export * from './generated/entity_resolution/Field';
export * from './generated/entity_resolution/FieldType';
export * from './generated/entity_resolution/Metric';
export * from './generated/entity_resolution/Feature';
export * from './generated/entity_resolution/Model';

const PROTO_PATH = `${__dirname}/../../../apps/entity-resolution/protos/ers.proto`;
const packageDefinition = loadSync(PROTO_PATH, {
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
});

const {
  entity_resolution: { EntityResolutionService },
} = loadPackageDefinition(packageDefinition) as unknown as ProtoGrpcType;

const getClient = (url: string, secure?: 'insecure' | 'ssl') =>
  new EntityResolutionService(
    url,
    secure === 'insecure' ? credentials.createInsecure() : credentials.createSsl(),
  );

export { EntityResolutionService, getClient };
