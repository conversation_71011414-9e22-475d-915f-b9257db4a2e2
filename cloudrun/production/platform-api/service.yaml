apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/client-name: cloud-console
    run.googleapis.com/ingress: internal-and-cloud-load-balancing
    run.googleapis.com/launch-stage: BETA
  generation: 16
  labels:
    cloud.googleapis.com/location: us-central1 # from-param: ${cloud_run_region}
  name: platform-api # from-param: ${service_name}
spec:
  template:
    metadata:
      name: platform-api-revision # from-param: ${revision_name}
      annotations:
        autoscaling.knative.dev/maxScale: 100 # from-param: ${max_scale}
        autoscaling.knative.dev/minScale: 2 # from-param: ${min_scale}
        run.googleapis.com/startup-cpu-boost: true
        run.googleapis.com/client-name: cloud-console
        run.googleapis.com/sessionAffinity: false
        run.googleapis.com/network-interfaces: UNDEFINED # from-param: ${network_interface}
        run.googleapis.com/vpc-access-egress: all-traffic
        run.googleapis.com/container-dependencies: '{"platform-api":["alloydb-auth-proxy", "otel-collector"]}'
        run.googleapis.com/secrets: "RA_API_DATABASE_URL:projects/************/secrets/RA_API_DATABASE_URL,RA_API_DATADOG_API_KEY:projects/************/secrets/RA_API_DATADOG_API_KEY,DOCTOPUS_DATABASE_URL:projects/************/secrets/DOCTOPUS_DATABASE_URL,RA_API_ENCRYPTION_KEY:projects/************/secrets/RA_API_ENCRYPTION_KEY,RA_API_JWT_SECRET:projects/************/secrets/RA_API_JWT_SECRET,RA_API_MAILGUN_API_KEY:projects/************/secrets/RA_API_MAILGUN_API_KEY,RA_API_PAYMENTS_AUTH_SECRET:projects/************/secrets/RA_API_PAYMENTS_AUTH_SECRET,GRAFANA_OTEL_TOKEN:projects/************/secrets/GRAFANA_OTEL_TOKEN,platform-otel-config:projects/************/secrets/platform-otel-config,RA_API_MAILGUN_SIGNING_KEY:projects/************/secrets/RA_API_MAILGUN_SIGNING_KEY,ES_ENDPOINT:projects/************/secrets/ES_ENDPOINT,ES_USERNAME:projects/************/secrets/ES_USERNAME,ES_PASSWORD:projects/************/secrets/ES_PASSWORD,PRESET_API_TOKEN:projects/************/secrets/PRESET_API_TOKEN,PRESET_API_SECRET:projects/************/secrets/PRESET_API_SECRET" # from-param: ${inject_secrets}
      labels:
        team: engineering
        product: core-platform
        service: platform-api # from-param: ${service_name}

    spec:
      serviceAccountName: UNDEFINED # from-param: ${service_account}
      timeoutSeconds: 300
      containerConcurrency: 200
      containers:
      - name: otel-collector
        image: otel/opentelemetry-collector-contrib:latest
        env:
        - name: GRAFANA_OTEL_TOKEN
          valueFrom:
            secretKeyRef:
              key: latest
              name: GRAFANA_OTEL_TOKEN
        startupProbe:
          tcpSocket:
            port: 13133
          initialDelaySeconds: 0
          timeoutSeconds: 1
          failureThreshold: 20
          periodSeconds: 2
        livenessProbe:
          httpGet:
            path: /
            port: 13133
          initialDelaySeconds: 0
          timeoutSeconds: 1
          failureThreshold: 3
          periodSeconds: 60
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        volumeMounts:
        - mountPath: /etc/otelcol-contrib
          name: otel-config
      - name: alloydb-auth-proxy
        image: gcr.io/alloydb-connectors/alloydb-auth-proxy:latest
        args: [ "--port=5432", "--health-check", "--http-address=0.0.0.0", "projects/core-platform-dev-beam/locations/us-central1/clusters/core-cluster/instances/primary" ] # from-param: ${alloydb_connection_uri}
        startupProbe:
          httpGet:
            host:
            path: /startup
            port: 9090
          timeoutSeconds: 1
          initialDelaySeconds: 0
          failureThreshold: 20
          periodSeconds: 2
      - name: platform-api # from-param: ${service_name}
        image: platform-api # from-param: ${docker_image}
        ports:
        - containerPort: 9091
          name: http1
        env:
        - name: PRESET_TEAM
          value: 3de9ac25
        - name: PRESET_WORKSPACE_ID
          value: 3495f251
        - name: SUPERSET_DOMAIN
          value: https://3495f251.beam-gcp-mpc.app.preset.io
        - name: GOOGLE_PROJECT_ID
          value: core-platform-prod-beam # from-param: ${google_project_id}
        - name: CORS_ORIGINS
          value: 'regex:^(https:\/\/)?app.([a-z]+\.)?bybeam.co$,regex:^(https:\/\/)?app.([a-z]+\.)?gcp.bybeam.co$'
        - name: ENVIRONMENT
          value: production # from-param: ${environment}
        - name: NODE_TLS_REJECT_UNAUTHORIZED # why are we disabling TLS?
          value: 0
        - name: GRAPHQL_PORT
          value: 9091
        - name: WEBHOOK_PORT
          value: 10090
        - name: LOAD_TEST_ENABLED
          value: false
        - name: WEB_APP_URL
          value: UNDEFINED # from-param: ${web_app_url}
        - name: PAYMENTS_URL
          value: UNDEFINED # from-param: ${payments_url}
        - name: PAYMENTS_AUTH_ID # this is not a secret
          value: UNDEFINED # from-param: ${payments_auth_id}
        - name: WEBHOOK_BASE_URL
          value: UNDEFINED # from-param: ${webhook_base_url}
        - name: VERIFICATION_URL
          value: UNDEFINED # from-param: ${verification_url}
        - name: CONFIG_SERVER_URL
          value: UNDEFINED # from-param: ${config_server_url}
        - name: NOTIFICATION_SERVER_URL
          value: UNDEFINED # from-param: ${notification_server_url}
        - name: LINKING_SERVER_URL
          value: UNDEFINED # from-param: ${linking_server_url}
        - name: IDENTITY_SERVER_URL
          value: UNDEFINED # from-param: ${identity_server_url}
        - name: ENTITY_RESOLUTION_URL
          value: UNDEFINED # from-param: ${entity_resolution_url}
        - name: USE_ELASTICSEARCH
          value: false # from-param: ${use_elasticsearch}
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_DATABASE_URL
        - name: DATADOG_API_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_DATADOG_API_KEY
        - name: DOCTOPUS_DATABASE_URL
          valueFrom:
            secretKeyRef:
              key: latest
              name: DOCTOPUS_DATABASE_URL
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_ENCRYPTION_KEY
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_JWT_SECRET
        - name: MAILGUN_API_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_MAILGUN_API_KEY
        - name: PAYMENTS_AUTH_SECRET
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_PAYMENTS_AUTH_SECRET
        - name: MAILGUN_SIGNING_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: RA_API_MAILGUN_SIGNING_KEY
        - name: ELASTICSEARCH_ENDPOINT
          valueFrom:
            secretKeyRef:
              key: latest
              name: ES_ENDPOINT
        - name: ELASTICSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              key: latest
              name: ES_USERNAME
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              key: latest
              name: ES_PASSWORD
        - name: PRESET_API_TOKEN
          valueFrom:
            secretKeyRef:
              key: latest
              name: PRESET_API_TOKEN
        - name: PRESET_API_SECRET
          valueFrom:
            secretKeyRef:
              key: latest
              name: PRESET_API_SECRET
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          failureThreshold: 20
          initialDelaySeconds: 0
          periodSeconds: 2
          tcpSocket:
            port: 9091
          timeoutSeconds: 1
        livenessProbe:
          httpGet:
            path: /healthy
            port: 9091
          initialDelaySeconds: 0
          timeoutSeconds: 10
          failureThreshold: 5
          periodSeconds: 30
      volumes:
      - name: otel-config
        secret:
          items:
          - key: latest
            path: config.yaml
          secretName: platform-otel-config
  traffic:
  - latestRevision: true
    percent: 100
