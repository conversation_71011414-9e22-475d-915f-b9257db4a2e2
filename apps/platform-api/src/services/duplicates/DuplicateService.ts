import { Document, Duplicate } from '@bybeam/platform-types';
import {
  DuplicateDocumentRepository,
  DuplicateRepository,
} from '@platform-api/@types/repositories/core.js';
import EntityResolutionRepository from '@platform-api/repositories/external/EntityResolutionRespository.js';
import { logger } from '../../utils/logger.js';
import QueryServiceImplementation from '../QueryService.js';
import { randomUUID } from 'node:crypto';

export default class DuplicateService extends QueryServiceImplementation<
  Duplicate,
  DuplicateRepository
> {
  repository: DuplicateRepository;
  private duplicateDocumentRepository: DuplicateDocumentRepository;
  private readonly entityResolutionRepo: EntityResolutionRepository;

  constructor({
    repository,
    duplicateDocumentRepository,
    entityResolutionRepo,
  }: {
    repository: DuplicateRepository;
    duplicateDocumentRepository: DuplicateDocumentRepository;
    entityResolutionRepo: EntityResolutionRepository;
  }) {
    super(repository);
    this.repository = repository;
    this.duplicateDocumentRepository = duplicateDocumentRepository;
    this.entityResolutionRepo = entityResolutionRepo;
  }

  public async recordDuplicateDocument(document: Document): Promise<void> {
    if (!document.sha256) {
      throw new Error('Document must have a sha256 hash');
    }
    const duplicates = await this.entityResolutionRepo.query({
      datasource: { database: { name: 'core', table: 'documents' }, source: 'database' },
      model: 'DUPLICATE_HASH',
      entities: [
        {
          entityId: document.id,
          fields: [{ fieldName: 'sha256', value: document.sha256, fieldType: 'HASH' }],
        },
      ],
      features: [{ queryField: 'sha256', candidateField: 'sha256', metric: 'EXACT_MATCH' }],
      _maxResults: 'maxResults',
      _confidenceThreshold: 'confidenceThreshold',
    });
    logger.debug({ duplicates }, 'recordDuplicateDocument: duplicates found');
    if (!duplicates.matches.length) {
      return;
    }
    const duplicateId = randomUUID();
    await this.repository.save({
      id: duplicateId,
      groupId: document.id,
      entityType: 'document',
      metadata: duplicates.matches,
    });
    await this.duplicateDocumentRepository.insert(
      duplicates.matches.map((match) => ({
        duplicateId,
        documentId: match.entityId,
      })),
    );
  }
}
