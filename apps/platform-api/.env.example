# Docker Environment
ENVIRONMENT=local
PORT=10010
TZ=UTC

# Database
DATABASE_URL=postgresql://main:password@localhost:9012/core
VERIFICATION_DATABASE_URL=postgresql://main:password@localhost:9020/verification
# In production only, we also use a read only replica for reporting queries
REPORTING_DATABASE_URL=***************************************************/core
# Uncomment this to turn off database logging locally. 
# SILENCE_DATABASE_LOGS=true

# READ_ONLY='true'

# Datadog
DATADOG_HOST=http-intake.logs.datadoghq.com
DATADOG_API_KEY=NotReal

# Web Application
WEB_APP_URL=http://localhost:3013
CORS_ORIGINS=http://localhost:3002,http://localhost:3013,http://localhost:9050,http://localhost:3014

# Webhook
WEBHOOK_BASE_URL=http://localhost

# Secrets
JWT_SECRET=SymmetricEncryptionKey!
ENCRYPTION_KEY=6f1b5e60a12044139f36e5b478960f8e607a5ee5fd01788fa6bd3419cf7b0751

# Mailgun
MAILGUN_API_KEY=AlsoNotARealKey
# Uncomment to enable sending actual emails locally
# SEND_EMAILS_OVERRIDE=true
MAILGUN_SIGNING_KEY=StillNotReal

#GCP
# This should be whatever is printed out when you run:
# gcloud auth application-default login
DOCTOPUS_DATABASE_URL=postgresql://main:password@localhost:9024/document_ai
GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/.config/gcloud/application_default_credentials.json
GOOGLE_PROJECT_ID=core-platform-local-beam

# Payments
PAYMENTS_URL=http://localhost:10012
PAYMENTS_AUTH_ID=e5ff6820-7a57-484a-9119-dd3997a22289
PAYMENTS_AUTH_SECRET=ZGQ2ZGZjOTQ2ZGYwOWY5YzMzMTVmODE4YmM1MDBmM2JkMjQxOTY0MGU0ZGJiNTQ0YzQ2ZTEzZTJlZmI4ZTkwNw==

# Load Testing
LOAD_TEST_ENABLED=false

# Verification Service
VERIFICATION_URL=localhost:10052

# PubSub
## emulator for local dev
PUBSUB_EMULATOR_HOST=0.0.0.0:8085
PUBSUB_PROJECT_ID=core-platform-local-beam

# Microservices
VERIFICATION_URL=localhost:10052
CONFIG_SERVER_URL=localhost:10056
NOTIFICATION_SERVER_URL=localhost:10057
LINKING_SERVER_URL=localhost:10058
IDENTITY_SERVER_URL=localhost:10059
ENTITY_RESOLUTION_URL=localhost:11051

# Elasticsearch
USE_ELASTICSEARCH=true
ELASTICSEARCH_SYNC_URL=http://localhost:12002
# For local development
ELASTICSEARCH_ENDPOINT=http://localhost:9200
# For cloud development
# ELASTICSEARCH_ENDPOINT=[See GCP Secret Manager: ES_ENDPOINT]
# ELASTICSEARCH_USERNAME=[See GCP Secret Manager: ES_USERNAME]
# ELASTICSEARCH_PASSWORD=[See GCP Secret Manager: ES_PASSWORD]

#Preset  
PRESET_API_TOKEN=<PRESET_API_TOKEN>
PRESET_API_SECRET=<PRESET_API_SECRET>

PRESET_TEAM=3de9ac25
PRESET_WORKSPACE_ID=2f088cc1
PRESET_DASHBOARD_ID=3914ae70-f479-4009-89e8-5c8ce1ccea97